<?php
require 'partials/_dbconnect.php';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Room Selection</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Room Selection</h1>
    
    <div>
        <label for="roomNo">Room Number:</label>
        <select name="roomNo" id="roomNo" onchange="selectdata(this.value)">
            <option value="">Select Number</option>
            <?php 
            $usersql = "SELECT * FROM `roomsdetails`";
            $userResult = mysqli_query($conn, $usersql);
            while($userRow = mysqli_fetch_assoc($userResult)){
                $roomNo = $userRow['room_no'];
                echo "<option value='" . $roomNo . "'>" . $roomNo . "</option>";
            }
            ?>
        </select>
    </div>
    
    <div>
        <label for="seater">Seater:</label>
        <input type="text" id="seater" readonly>
    </div>
    
    <div>
        <label for="fees">Fees:</label>
        <input type="text" id="fees" readonly>
    </div>
    
    <div id="availability-status"></div>
    
    <script>
    function selectdata(no) {
        console.log("Room selected: " + no);
        
        // Get seater
        $.ajax({
            url: 'fetch-data.php',
            method: 'post',
            data: 'room=' + no,
            success: function(result) {
                console.log("Seater result: " + result);
                $('#seater').val(result);
            },
            error: function(xhr, status, error) {
                console.error("Seater AJAX error: " + error);
            }
        });
        
        // Get fees
        $.ajax({
            url: 'fetch-data.php',
            method: 'post',
            data: 'roomid=' + no,
            success: function(result) {
                console.log("Fees result: " + result);
                $('#fees').val(result);
            },
            error: function(xhr, status, error) {
                console.error("Fees AJAX error: " + error);
            }
        });
        
        // Check availability
        $.ajax({
            url: 'fetch-data.php',
            method: 'post',
            data: {roomsno: no},
            dataType: "JSON",
            success: function(result) {
                console.log("Availability result: ", result);
                $("#availability-status").html(result.msg);
            },
            error: function(xhr, status, error) {
                console.error("Availability AJAX error: " + error);
                console.error(xhr.responseText);
            }
        });
    }
    </script>
</body>
</html>
