-- Create student_passwords table
CREATE TABLE `student_passwords` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `password` varchar(300) NOT NULL,
  `entry_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`),
  CONSTRAINT `fk_student_id` FOREIGN KEY (`student_id`) REFERENCES `userregistration` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Create active_bookings table for tracking concurrent booking attempts
CREATE TABLE `active_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_no` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('in_progress','completed','cancelled') NOT NULL DEFAULT 'in_progress',
  <PERSON>IMAR<PERSON> KEY (`id`),
  <PERSON><PERSON><PERSON> `room_no` (`room_no`),
  KEY `student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Add a trigger to automatically remove stale booking attempts (older than 10 minutes)
DELIMITER //
CREATE EVENT `cleanup_stale_bookings`
ON SCHEDULE EVERY 1 MINUTE
DO
BEGIN
  DELETE FROM `active_bookings` 
  WHERE `status` = 'in_progress' 
  AND `start_time` < NOW() - INTERVAL 10 MINUTE;
END//
DELIMITER ;
