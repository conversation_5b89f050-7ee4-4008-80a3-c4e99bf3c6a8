<?php
require 'partials/_dbconnect.php';

// Get all rooms
$roomsql = "SELECT * FROM `roomsdetails`";
$roomResult = mysqli_query($conn, $roomsql);

echo "<h2>Available Rooms:</h2>";
echo "<ul>";
while($roomRow = mysqli_fetch_assoc($roomResult)){
    echo "<li>Room: " . $roomRow['room_no'] . ", Seater: " . $roomRow['seater'] . ", Fees: " . $roomRow['fees'] . "</li>";
}
echo "</ul>";

// Test the fetch-data.php functionality
if(isset($_GET['room'])) {
    $roomNo = $_GET['room'];
    
    echo "<h2>Testing fetch-data.php for Room $roomNo:</h2>";
    
    // Test seater retrieval
    $roomsql = "SELECT seater FROM `roomsdetails` where room_no='$roomNo'";
    $roomResult = mysqli_query($conn, $roomsql);
    $seaterdata = mysqli_fetch_assoc($roomResult);
    echo "<p>Seater: " . $seaterdata['seater'] . "</p>";
    
    // Test fees retrieval
    $roomsql = "SELECT fees FROM `roomsdetails` where room_no='$roomNo'";
    $roomResult = mysqli_query($conn, $roomsql);
    $feesdata = mysqli_fetch_assoc($roomResult);
    echo "<p>Fees: " . $feesdata['fees'] . "</p>";
    
    // Test availability
    $roomDetailsSql = "SELECT seater FROM `roomsdetails` where room_no='$roomNo'";
    $roomDetailsResult = mysqli_query($conn, $roomDetailsSql);
    $roomDetailsData = mysqli_fetch_assoc($roomDetailsResult);
    $totalSeater = $roomDetailsData['seater'];
    
    $roomssql = "SELECT count(*) as roomno FROM `hostelbookings` where roomno='$roomNo'";
    $roomsResult = mysqli_query($conn, $roomssql);
    $availabledata = mysqli_fetch_assoc($roomsResult);
    $bookedSeats = $availabledata['roomno'];
    
    $availableSeats = $totalSeater - $bookedSeats;
    
    echo "<p>Total Seater: $totalSeater</p>";
    echo "<p>Booked Seats: $bookedSeats</p>";
    echo "<p>Available Seats: $availableSeats</p>";
}
?>

<h2>Test Room Selection:</h2>
<form method="get">
    <select name="room">
        <option value="">Select Room</option>
        <?php 
        $roomsql = "SELECT * FROM `roomsdetails`";
        $roomResult = mysqli_query($conn, $roomsql);
        while($roomRow = mysqli_fetch_assoc($roomResult)){
            echo "<option value='" . $roomRow['room_no'] . "'>" . $roomRow['room_no'] . "</option>";
        }
        ?>
    </select>
    <button type="submit">Test</button>
</form>

<h2>Back to Hostel Booking:</h2>
<a href="hostelManage.php">Go to Hostel Booking</a>
