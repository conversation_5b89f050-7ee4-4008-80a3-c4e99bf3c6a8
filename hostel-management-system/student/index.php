<?php
session_start();

// Check if student is logged in
if(!isset($_SESSION['studentloggedin']) || $_SESSION['studentloggedin']!=true){
    header("location: /hostel-management-system/login.php");
    exit;
}

require '../partials/_dbconnect.php';
$studentId = $_SESSION['studentId'];
$studentRegNo = $_SESSION['studentregno'];
$studentName = $_SESSION['studentname'];

// Check if student has a booking
$bookingSql = "SELECT * FROM hostelbookings WHERE regno = '$studentRegNo'";
$bookingResult = mysqli_query($conn, $bookingSql);
$hasBooking = mysqli_num_rows($bookingResult) > 0;
$bookingDetails = $hasBooking ? mysqli_fetch_assoc($bookingResult) : null;
?>

<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
    <title>Student Dashboard</title>
    <link rel = "icon" href ="/hostel-management-system/img/hostel-image.png" type = "image/x-icon">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link href='https://cdn.jsdelivr.net/npm/boxicons@2.0.5/css/boxicons.min.css' rel='stylesheet'>
    <link rel="stylesheet" href="../assets/css/styles.css">
</head>
<body id="body-pd" style="background: #80808045;">
    <!-- Student Navigation Bar -->
    <header class="header" id="header">
        <div class="header__toggle">
            <i class='bx bx-menu' id="header-toggle"></i>
        </div>
        <div class="header__img">
            <img src="/hostel-management-system/img/hostel-image.png" alt="">
        </div>
    </header>

    <div class="l-navbar" id="nav-bar">
        <nav class="nav">
            <div>
                <a href="#" class="nav__logo">
                    <i class='bx bx-building-house nav__logo-icon'></i>
                    <span class="nav__logo-name">Hostel Management</span>
                </a>

                <div class="nav__list">
                    <a href="index.php" class="nav__link active">
                        <i class='bx bx-grid-alt nav__icon'></i>
                        <span class="nav__name">Dashboard</span>
                    </a>
                    <a href="book_room.php" class="nav__link">
                        <i class='bx bx-bed nav__icon'></i>
                        <span class="nav__name">Book Room</span>
                    </a>
                    <a href="profile.php" class="nav__link">
                        <i class='bx bx-user nav__icon'></i>
                        <span class="nav__name">Profile</span>
                    </a>
                </div>
            </div>

            <a href="../partials/_logout.php" class="nav__link">
                <i class='bx bx-log-out nav__icon'></i>
                <span class="nav__name">Log Out</span>
            </a>
        </nav>
    </div>

    <?php
    if(isset($_GET['loginsuccess']) && $_GET['loginsuccess']=="true"){
        echo '<div class="alert alert-success alert-dismissible fade show" role="alert" style="width:100%">
                <strong>Success!</strong> You are logged in
                <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span></button>
              </div>';
    }
    ?>

    <div class="container-fluid" style="margin-top:98px">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header" style="background-color: rgb(111 202 203);">
                        <h5>Welcome, <?php echo $studentName; ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h4>Your Details</h4>
                                <p><strong>Registration Number:</strong> <?php echo $studentRegNo; ?></p>
                                
                                <?php if($hasBooking): ?>
                                <div class="alert alert-success">
                                    <h5>Your Room Booking</h5>
                                    <p><strong>Room Number:</strong> <?php echo $bookingDetails['roomno']; ?></p>
                                    <p><strong>Seater:</strong> <?php echo $bookingDetails['seater']; ?></p>
                                    <p><strong>From Date:</strong> <?php echo date('d-m-Y', strtotime($bookingDetails['stayfrom'])); ?></p>
                                    <p><strong>Duration:</strong> <?php echo $bookingDetails['duration']; ?> months</p>
                                    <p><strong>Food Status:</strong> <?php echo $bookingDetails['foodstatus'] ? 'Required' : 'Not Required'; ?></p>
                                    <p><strong>Total Amount:</strong> ₹<?php echo $bookingDetails['total_amount']; ?></p>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-warning">
                                    <p>You haven't booked a room yet. <a href="book_room.php" class="btn btn-primary btn-sm">Book Now</a></p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Optional JavaScript -->
    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js" integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>         
    <script src="https://unpkg.com/bootstrap-show-password@1.2.1/dist/bootstrap-show-password.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
