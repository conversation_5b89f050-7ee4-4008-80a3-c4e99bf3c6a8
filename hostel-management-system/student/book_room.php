<?php
session_start();

// Check if student is logged in
if(!isset($_SESSION['studentloggedin']) || $_SESSION['studentloggedin']!=true){
    header("location: /hostel-management-system/login.php");
    exit;
}

require '../partials/_dbconnect.php';
$studentId = $_SESSION['studentId'];
$studentRegNo = $_SESSION['studentregno'];
$studentName = $_SESSION['studentname'];

// Check if student already has a booking
$bookingSql = "SELECT * FROM hostelbookings WHERE regno = '$studentRegNo'";
$bookingResult = mysqli_query($conn, $bookingSql);
$hasBooking = mysqli_num_rows($bookingResult) > 0;

// Get student details
$studentSql = "SELECT * FROM userregistration WHERE id = '$studentId'";
$studentResult = mysqli_query($conn, $studentSql);
$studentDetails = mysqli_fetch_assoc($studentResult);
?>

<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
    <title>Book Room</title>
    <link rel = "icon" href ="/hostel-management-system/img/hostel-image.png" type = "image/x-icon">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link href='https://cdn.jsdelivr.net/npm/boxicons@2.0.5/css/boxicons.min.css' rel='stylesheet'>
    <link rel="stylesheet" href="../assets/css/styles.css">
</head>
<body id="body-pd" style="background: #80808045;">
    <!-- Student Navigation Bar -->
    <header class="header" id="header">
        <div class="header__toggle">
            <i class='bx bx-menu' id="header-toggle"></i>
        </div>
        <div class="header__img">
            <img src="/hostel-management-system/img/hostel-image.png" alt="">
        </div>
    </header>

    <div class="l-navbar" id="nav-bar">
        <nav class="nav">
            <div>
                <a href="#" class="nav__logo">
                    <i class='bx bx-building-house nav__logo-icon'></i>
                    <span class="nav__logo-name">Hostel Management</span>
                </a>

                <div class="nav__list">
                    <a href="index.php" class="nav__link">
                        <i class='bx bx-grid-alt nav__icon'></i>
                        <span class="nav__name">Dashboard</span>
                    </a>
                    <a href="book_room.php" class="nav__link active">
                        <i class='bx bx-bed nav__icon'></i>
                        <span class="nav__name">Book Room</span>
                    </a>
                    <a href="profile.php" class="nav__link">
                        <i class='bx bx-user nav__icon'></i>
                        <span class="nav__name">Profile</span>
                    </a>
                </div>
            </div>

            <a href="../partials/_logout.php" class="nav__link">
                <i class='bx bx-log-out nav__icon'></i>
                <span class="nav__name">Log Out</span>
            </a>
        </nav>
    </div>

    <div class="container-fluid" style="margin-top:98px">
        <?php if($hasBooking): ?>
        <div class="alert alert-warning">
            <h5>You already have a room booking</h5>
            <p>You cannot book multiple rooms. Please check your <a href="index.php">dashboard</a> for details.</p>
        </div>
        <?php else: ?>
        <div class="col-lg-12">
            <div class="row">
                <!-- FORM Panel -->
                <div class="col-md-12">
                    <form action="../partials/_studentBooking.php" method="post" enctype="multipart/form-data">
                        <div class="card">
                            <div class="card-header" style="background-color: rgb(111 202 203);">
                                Book a Hostel Room
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="form-group col-md-4">
                                        <label for="roomno">Room Number:</label>
                                        <select name="roomNo" id="roomNo" class="custom-select browser-default" required onchange="checkRoomAvailability(this.value)">
                                            <option value="">Select Number</option>
                                            <?php
                                            $usersql = "SELECT * FROM `roomsdetails`";
                                            $userResult = mysqli_query($conn, $usersql);
                                            while($userRow = mysqli_fetch_assoc($userResult)){
                                                $roomNo = $userRow['room_no'];
                                                echo '<option value="'.$roomNo.'">'.$roomNo.'</option>';
                                            }
                                            ?>
                                        </select>
                                        <div id="room-status-container" style="margin-top:10px; padding:5px; border-radius:5px;">
                                            <span id="availability-status" style="font-size:14px; font-weight:bold;"></span>
                                            <div id="active-bookings-status" style="font-size:14px; margin-top:5px;"></div>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Start Date: </label>
                                        <input type="date" class="form-control" name="startdate" required>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Seater: </label>
                                        <input type="text" class="form-control" name="seater" id="seater" placeholder="Enter Seater No." required readonly>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-md-4">
                                        <label for="duration">Total Duration:</label>
                                        <select name="duration" id="duration" class="custom-select browser-default" required>
                                            <option value="">Choose Duration</option>
                                            <?php for($i=1; $i<=12; $i++): ?>
                                                <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="food">Food Status:</label>
                                        <select name="foodstatus" id="foodstatus" class="custom-select browser-default" required>
                                            <option value="">Select Status</option>
                                            <option value="1">Required (Extra 4000 Rs. per month)</option>
                                            <option value="0">Not Required</option>
                                        </select>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Total Fees Per Month: </label>
                                        <input type="text" class="form-control" name="fees" id="fees" placeholder="Fees per Month" required readonly>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Total Amount: </label>
                                        <input type="text" class="form-control" name="total_ammount" id="total_ammount" required placeholder="Total amount" readonly>
                                    </div>
                                </div>

                                <!-- Hidden fields for student information -->
                                <input type="hidden" name="reg_no" value="<?php echo $studentRegNo; ?>">
                                <input type="hidden" name="first_name" value="<?php echo $studentDetails['first_name']; ?>">
                                <input type="hidden" name="last_name" value="<?php echo $studentDetails['last_name']; ?>">
                                <input type="hidden" name="gender" value="<?php echo $studentDetails['gender']; ?>">
                                <input type="hidden" name="phone" value="<?php echo $studentDetails['contact_no']; ?>">
                                <input type="hidden" name="emailid" value="<?php echo $studentDetails['emailid']; ?>">

                                <!-- Additional required fields -->
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <h5>Additional Information</h5>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Emergency Contact Number: </label>
                                        <input type="text" class="form-control" name="emg_no" required placeholder="Enter emergency number" maxlength="10">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="food">Preferred Course:</label>
                                        <select name="course" id="course" class="custom-select browser-default" required>
                                            <option value="">Select Course</option>
                                            <?php
                                            $coursesql = "SELECT course_fn FROM `courses`";
                                            $courseResult = mysqli_query($conn, $coursesql);
                                            while($courseRow = mysqli_fetch_assoc($courseResult)){
                                                echo '<option value="'.$courseRow['course_fn'].'">'.$courseRow['course_fn'].'</option>';
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Guardian Information -->
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <h5>Guardian's Information</h5>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Guardian Name: </label>
                                        <input type="text" class="form-control" name="guardian_name" required placeholder="Enter guardian name">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Relation: </label>
                                        <input type="text" class="form-control" name="relation" required placeholder="Enter relation">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Contact Number: </label>
                                        <input type="text" class="form-control" name="contact_number" required placeholder="Enter contact number" maxlength="10">
                                    </div>
                                </div>

                                <!-- Address Information -->
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <h5>Address Information</h5>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-md-4">
                                        <label for="food">State:</label>
                                        <select name="state" id="state" class="custom-select browser-default" required>
                                            <option value="">Select State</option>
                                            <?php
                                            $statesql = "SELECT State FROM `state_master`";
                                            $stateResult = mysqli_query($conn, $statesql);
                                            while($stateRow = mysqli_fetch_assoc($stateResult)){
                                                echo '<option value="'.$stateRow['State'].'">'.$stateRow['State'].'</option>';
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="control-label">City: </label>
                                        <input type="text" class="form-control" name="city" required placeholder="Enter city name">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Address: </label>
                                        <textarea class="form-control" name="address" required placeholder="Enter Address" rows="3"></textarea>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="control-label">Postal Code: </label>
                                        <input type="text" class="form-control" name="postal_code" required placeholder="Enter postal code">
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="row">
                                    <div class="col-md-12">
                                        <button type="submit" name="createHostal" id="createHostal" class="btn btn-sm btn-primary col-sm-3 offset-md-4"> Book Room </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- FORM Panel -->
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Optional JavaScript -->
    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js" integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>
    <script src="https://unpkg.com/bootstrap-show-password@1.2.1/dist/bootstrap-show-password.min.js"></script>
    <script src="../assets/js/main.js"></script>

    <script type="text/javascript">
    // Variable to store the interval ID for polling
    let pollingInterval = null;

    function checkRoomAvailability(roomNo) {
        if (roomNo === '') {
            clearInterval(pollingInterval);
            $('#availability-status').html('');
            $('#active-bookings-status').html('');
            $('#seater').val('');
            $('#fees').val('');
            $('#total_ammount').val('');
            return;
        }

        // Start polling for room availability and active bookings
        clearInterval(pollingInterval);
        pollingInterval = setInterval(function() {
            fetchRoomStatus(roomNo);
        }, 1000); // Poll every 1 second for more responsive updates

        // Initial check
        fetchRoomStatus(roomNo);

        // Also get the seater and fees info
        $.ajax({
            url: '../fetch-data.php',
            method: 'post',
            data: 'room='+roomNo,
            success: function(result) {
                $('#seater').val(result);
            }
        });

        $.ajax({
            url: '../fetch-data.php',
            method: 'post',
            data: 'roomid='+roomNo,
            success: function(result) {
                $('#fees').val(result);
            }
        });
    }

    function fetchRoomStatus(roomNo) {
        $.ajax({
            url: '../fetch-room-status.php',
            method: 'post',
            data: {roomNo: roomNo},
            dataType: "JSON",
            success: function(result) {
                // Update availability status
                $('#availability-status').html(result.availabilityMsg);

                // Update active bookings status and form UI based on room status
                if (result.roomFull) {
                    // Room is already fully booked
                    let message = '<div class="alert alert-danger">' +
                        '<strong>Room Full:</strong> This room has no available seats. Please select another room.' +
                        '</div>';
                    $('#active-bookings-status').html(message);
                    $('#createHostal').prop('disabled', true);

                    // Add visual indication that the room is full
                    $('#room-status-container').addClass('border border-danger p-2');
                }
                else if (result.potentiallyFull) {
                    // Room will be full if all active bookings complete
                    let message = '<div class="alert alert-danger">' +
                        '<strong>Warning:</strong> This room is currently being booked by ' + result.activeBookings +
                        ' other user(s) and will likely be full. Please select another room.' +
                        '</div>';
                    $('#active-bookings-status').html(message);
                    $('#createHostal').prop('disabled', true);

                    // Add visual indication that the room is being booked
                    $('#room-status-container').addClass('border border-warning p-2');
                }
                else if (result.activeBookings > 0) {
                    // Room has active booking attempts but still has space
                    let message = '<div class="alert alert-warning">' +
                        '<strong>Notice:</strong> ' + result.activeBookings + ' other user(s) are currently trying to book this room. ' +
                        'There are ' + result.availableSeats + ' seats available out of ' + result.totalSeater + '.' +
                        '</div>';
                    $('#active-bookings-status').html(message);
                    $('#createHostal').prop('disabled', false);

                    // Add visual indication that the room has active bookings
                    $('#room-status-container').addClass('border border-warning p-2');
                }
                else {
                    // No active bookings
                    $('#active-bookings-status').html('');
                    $('#createHostal').prop('disabled', false);
                    $('#room-status-container').removeClass('border border-danger border-warning p-2');
                }
            },
            error: function() {
                // Handle AJAX error
                $('#availability-status').html('<span style="color:red">Error checking room status. Please try again.</span>');
                $('#active-bookings-status').html('');
            }
        });
    }

    $(document).ready(function() {
        // Calculate total amount when duration or food status changes
        $('#duration, #foodstatus').change(function() {
            var foodstatus = $("#foodstatus").val();
            var duration = $("#duration").val();
            var fees = $("#fees").val();

            if (duration && fees) {
                var total_amt;
                if (foodstatus == 1) {
                    total_amt = duration * fees + 4000;
                } else {
                    total_amt = duration * fees;
                }
                $('#total_ammount').val(total_amt);
            }
        });

        // Reset form values when room changes
        $('#roomNo').change(function() {
            // Reset form fields
            $('#duration').val('');
            $('#total_ammount').val('');
            $('#foodstatus').val('');

            // Reset room status container styling
            $('#room-status-container').removeClass('border border-danger border-warning p-2');
            $('#availability-status').html('');
            $('#active-bookings-status').html('');

            // Re-enable the submit button (will be disabled again if needed)
            $('#createHostal').prop('disabled', false);
        });
    });

    // Clean up polling when page is unloaded
    $(window).on('beforeunload', function() {
        clearInterval(pollingInterval);
    });
    </script>
</body>
</html>
