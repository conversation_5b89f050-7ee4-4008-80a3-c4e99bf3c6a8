<?php
require 'partials/_dbconnect.php';

// Check if there are any existing students
$checkStudentsSql = "SELECT * FROM userregistration";
$studentsResult = mysqli_query($conn, $checkStudentsSql);
$existingStudents = mysqli_num_rows($studentsResult);

echo "<h2>Adding Test Students</h2>";

if ($existingStudents > 0) {
    echo "<p>Found $existingStudents existing students in the database.</p>";
    
    // Display existing students
    echo "<h3>Existing Students:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Registration No</th><th>Name</th><th>Email</th></tr>";
    
    while ($row = mysqli_fetch_assoc($studentsResult)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['registration_no'] . "</td>";
        echo "<td>" . $row['first_name'] . " " . $row['last_name'] . "</td>";
        echo "<td>" . $row['emailid'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No existing students found. Adding test students...</p>";
    
    // Add test students
    $testStudents = [
        [
            'reg_no' => 'S001',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'gender' => 'Male',
            'contact' => '1234567890',
            'email' => '<EMAIL>'
        ],
        [
            'reg_no' => 'S002',
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'gender' => 'Female',
            'contact' => '9876543210',
            'email' => '<EMAIL>'
        ],
        [
            'reg_no' => 'S003',
            'first_name' => 'Bob',
            'last_name' => 'Johnson',
            'gender' => 'Male',
            'contact' => '5555555555',
            'email' => '<EMAIL>'
        ]
    ];
    
    foreach ($testStudents as $student) {
        $reg_no = mysqli_real_escape_string($conn, $student['reg_no']);
        $first_name = mysqli_real_escape_string($conn, $student['first_name']);
        $last_name = mysqli_real_escape_string($conn, $student['last_name']);
        $gender = mysqli_real_escape_string($conn, $student['gender']);
        $contact = mysqli_real_escape_string($conn, $student['contact']);
        $email = mysqli_real_escape_string($conn, $student['email']);
        
        $insertSql = "INSERT INTO userregistration (registration_no, first_name, last_name, gender, contact_no, emailid) 
                      VALUES ('$reg_no', '$first_name', '$last_name', '$gender', '$contact', '$email')";
        
        if (mysqli_query($conn, $insertSql)) {
            echo "<p>Added student: $first_name $last_name ($reg_no)</p>";
        } else {
            echo "<p>Error adding student $reg_no: " . mysqli_error($conn) . "</p>";
        }
    }
}

// Now set up passwords for all students
echo "<h3>Setting Up Student Passwords</h3>";

// Get all students
$allStudentsSql = "SELECT * FROM userregistration";
$allStudentsResult = mysqli_query($conn, $allStudentsSql);

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Registration No</th><th>Name</th><th>Password</th></tr>";

while ($student = mysqli_fetch_assoc($allStudentsResult)) {
    $studentId = $student['id'];
    $regNo = $student['registration_no'];
    $name = $student['first_name'] . ' ' . $student['last_name'];
    
    // Check if password already exists
    $checkPasswordSql = "SELECT * FROM student_passwords WHERE student_id = '$studentId'";
    $passwordResult = mysqli_query($conn, $checkPasswordSql);
    
    if (mysqli_num_rows($passwordResult) == 0) {
        // Create a password (using "password" for all test accounts)
        $password = "password";
        $hashedPassword = md5($password);
        
        $insertPasswordSql = "INSERT INTO student_passwords (student_id, password) 
                             VALUES ('$studentId', '$hashedPassword')";
        
        if (mysqli_query($conn, $insertPasswordSql)) {
            echo "<tr><td>$regNo</td><td>$name</td><td>$password</td></tr>";
        } else {
            echo "<tr><td>$regNo</td><td>$name</td><td>Error: " . mysqli_error($conn) . "</td></tr>";
        }
    } else {
        echo "<tr><td>$regNo</td><td>$name</td><td>password (already set)</td></tr>";
    }
}

echo "</table>";

echo "<h3>Login Instructions</h3>";
echo "<p>You can now log in using any of the registration numbers above with the password 'password'.</p>";
echo "<p><a href='login.php'>Go to Login Page</a></p>";
?>
