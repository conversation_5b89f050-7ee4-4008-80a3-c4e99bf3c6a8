<?php
require 'partials/_dbconnect.php';

if(!empty($_POST['roomNo'])){
    // Sanitize input
    $roomNo = mysqli_real_escape_string($conn, $_POST['roomNo']);

    // Create a lock name based on room number for consistent reads
    $lockName = "room_status_check_" . $roomNo;

    // Try to acquire a lock
    if (acquireLock($lockName, 5)) { // Short timeout since this is just a read operation
        try {
            // Start a transaction for consistent reads
            beginTransaction();

            // First get the seater value from roomsdetails
            $roomDetailsSql = "SELECT seater FROM `roomsdetails` WHERE room_no='$roomNo'";
            $roomDetailsResult = executeQuery($roomDetailsSql);

            if (!$roomDetailsResult || mysqli_num_rows($roomDetailsResult) == 0) {
                // Room not found
                rollbackTransaction();
                releaseLock($lockName);
                echo json_encode(array(
                    "success" => 0,
                    "availabilityMsg" => '<span style="color:red">Room not found.</span>',
                    "activeBookings" => 0,
                    "availableSeats" => 0
                ));
                exit;
            }

            $roomDetailsData = mysqli_fetch_assoc($roomDetailsResult);
            $totalSeater = $roomDetailsData['seater'];

            // Then count how many bookings exist for this room
            $roomsSql = "SELECT COUNT(*) as booked_seats FROM `hostelbookings` WHERE roomno='$roomNo'";
            $roomsResult = executeQuery($roomsSql);

            if (!$roomsResult) {
                // Error counting bookings
                rollbackTransaction();
                releaseLock($lockName);
                echo json_encode(array(
                    "success" => 0,
                    "availabilityMsg" => '<span style="color:red">Error checking bookings.</span>',
                    "activeBookings" => 0,
                    "availableSeats" => 0
                ));
                exit;
            }

            $availableData = mysqli_fetch_assoc($roomsResult);
            $bookedSeats = $availableData['booked_seats'];

            // Count active booking attempts for this room
            $activeBookingsSql = "SELECT COUNT(*) as active_count FROM `active_bookings`
                                 WHERE room_no='$roomNo' AND status='in_progress'";
            $activeBookingsResult = executeQuery($activeBookingsSql);
            $activeBookingsData = mysqli_fetch_assoc($activeBookingsResult);
            $activeBookings = $activeBookingsData['active_count'];

            // Calculate available seats
            // Consider both confirmed bookings and active booking attempts
            // For UI purposes, we'll count active bookings as potential bookings
            $potentialBookings = $bookedSeats + $activeBookings;
            $availableSeats = $totalSeater - $bookedSeats; // Actual available seats (not counting active bookings)
            $potentialAvailableSeats = $totalSeater - $potentialBookings; // Available seats if all active bookings complete

            // Commit transaction and release lock
            commitTransaction();
            releaseLock($lockName);

            // Prepare response
            $response = array(
                "success" => 1,
                "activeBookings" => $activeBookings,
                "availableSeats" => $availableSeats,
                "potentialAvailableSeats" => $potentialAvailableSeats,
                "totalSeater" => $totalSeater,
                "bookedSeats" => $bookedSeats,
                "roomFull" => ($availableSeats <= 0),
                "potentiallyFull" => ($potentialAvailableSeats <= 0)
            );

            // Set availability message
            if ($availableSeats <= 0) {
                // Room is already full based on confirmed bookings
                $response["availabilityMsg"] = '<span style="color:red">Room is already fully booked.</span>';
                $response["success"] = 0;
            } else if ($potentialAvailableSeats <= 0) {
                // Room will be full if all active bookings complete
                $response["availabilityMsg"] = '<span style="color:red">Room is currently being booked by other users and will likely be full.</span>';
                $response["success"] = 0;
            } else if ($activeBookings > 0) {
                // Room has active booking attempts but still has space
                $response["availabilityMsg"] = '<span style="color:orange">' . $availableSeats . ' of ' . $totalSeater . ' seats available. ' . $activeBookings . ' booking(s) in progress.</span>';
            } else if ($bookedSeats > 0) {
                // Room has some bookings but no active attempts
                $response["availabilityMsg"] = '<span style="color:green">' . $availableSeats . ' of ' . $totalSeater . ' seats available.</span>';
            } else {
                // Room is completely empty
                $response["availabilityMsg"] = '<span style="color:green">All ' . $totalSeater . ' seats available.</span>';
            }

            echo json_encode($response);

        } catch (Exception $e) {
            // Handle any exceptions
            rollbackTransaction();
            releaseLock($lockName);

            // Log the error
            error_log("Exception during room status check: " . $e->getMessage());

            echo json_encode(array(
                "success" => 0,
                "availabilityMsg" => '<span style="color:red">Error checking availability.</span>',
                "activeBookings" => 0,
                "availableSeats" => 0
            ));
        }
    } else {
        // Could not acquire lock, but this is just a read operation, so we can still proceed
        // with a non-transactional read (might be slightly inconsistent but acceptable for UI)

        // First get the seater value from roomsdetails
        $roomDetailsSql = "SELECT seater FROM `roomsdetails` WHERE room_no='$roomNo'";
        $roomDetailsResult = mysqli_query($conn, $roomDetailsSql);

        if (!$roomDetailsResult || mysqli_num_rows($roomDetailsResult) == 0) {
            // Room not found
            echo json_encode(array(
                "success" => 0,
                "availabilityMsg" => '<span style="color:red">Room not found.</span>',
                "activeBookings" => 0,
                "availableSeats" => 0
            ));
            exit;
        }

        $roomDetailsData = mysqli_fetch_assoc($roomDetailsResult);
        $totalSeater = $roomDetailsData['seater'];

        // Then count how many bookings exist for this room
        $roomsSql = "SELECT COUNT(*) as booked_seats FROM `hostelbookings` WHERE roomno='$roomNo'";
        $roomsResult = mysqli_query($conn, $roomsSql);

        if (!$roomsResult) {
            // Error counting bookings
            echo json_encode(array(
                "success" => 0,
                "availabilityMsg" => '<span style="color:red">Error checking bookings.</span>',
                "activeBookings" => 0,
                "availableSeats" => 0
            ));
            exit;
        }

        $availableData = mysqli_fetch_assoc($roomsResult);
        $bookedSeats = $availableData['booked_seats'];

        // Count active booking attempts for this room
        $activeBookingsSql = "SELECT COUNT(*) as active_count FROM `active_bookings`
                             WHERE room_no='$roomNo' AND status='in_progress'";
        $activeBookingsResult = mysqli_query($conn, $activeBookingsSql);
        $activeBookingsData = mysqli_fetch_assoc($activeBookingsResult);
        $activeBookings = $activeBookingsData['active_count'];

        // Calculate available seats
        // Consider both confirmed bookings and active booking attempts
        // For UI purposes, we'll count active bookings as potential bookings
        $potentialBookings = $bookedSeats + $activeBookings;
        $availableSeats = $totalSeater - $bookedSeats; // Actual available seats (not counting active bookings)
        $potentialAvailableSeats = $totalSeater - $potentialBookings; // Available seats if all active bookings complete

        // Prepare response
        $response = array(
            "success" => 1,
            "activeBookings" => $activeBookings,
            "availableSeats" => $availableSeats,
            "potentialAvailableSeats" => $potentialAvailableSeats,
            "totalSeater" => $totalSeater,
            "bookedSeats" => $bookedSeats,
            "roomFull" => ($availableSeats <= 0),
            "potentiallyFull" => ($potentialAvailableSeats <= 0)
        );

        // Set availability message
        if ($availableSeats <= 0) {
            // Room is already full based on confirmed bookings
            $response["availabilityMsg"] = '<span style="color:red">Room is already fully booked.</span>';
            $response["success"] = 0;
        } else if ($potentialAvailableSeats <= 0) {
            // Room will be full if all active bookings complete
            $response["availabilityMsg"] = '<span style="color:red">Room is currently being booked by other users and will likely be full.</span>';
            $response["success"] = 0;
        } else if ($activeBookings > 0) {
            // Room has active booking attempts but still has space
            $response["availabilityMsg"] = '<span style="color:orange">' . $availableSeats . ' of ' . $totalSeater . ' seats available. ' . $activeBookings . ' booking(s) in progress.</span>';
        } else if ($bookedSeats > 0) {
            // Room has some bookings but no active attempts
            $response["availabilityMsg"] = '<span style="color:green">' . $availableSeats . ' of ' . $totalSeater . ' seats available.</span>';
        } else {
            // Room is completely empty
            $response["availabilityMsg"] = '<span style="color:green">All ' . $totalSeater . ' seats available.</span>';
        }

        echo json_encode($response);
    }
}
?>
