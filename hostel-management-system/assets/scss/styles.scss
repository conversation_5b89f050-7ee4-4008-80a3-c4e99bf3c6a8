/*===== GOOGLE FONTS =====*/
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap');

/*===== VARIABLES CSS =====*/
:root{
    --header-height: 3rem;
    --nav-width: 68px;

    /*===== Colors =====*/
    --first-color: #4723D9;
    --first-color-light: #AFA5D9;
    --white-color: #F7F6FB;

    /*===== Font and typography =====*/
    --body-font: 'Nunito', sans-serif;
    --normal-font-size: 1rem;
  
    /*===== z index =====*/
    --z-fixed: 100;
}

/*===== BASE =====*/
*,::before,::after{
    box-sizing: border-box;
}

body{
    position: relative;
    margin: var(--header-height) 0 0 0;
    padding: 0 1rem;
    font-family: var(--body-font);
    font-size: var(--normal-font-size);
    transition: .5s;
}

a{
    text-decoration: none;
}

/*===== HEADER =====*/
.header{
    width: 100%;
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    background-color: var(--white-color);
    z-index: var(--z-fixed);
    transition: .5s;

    &__toggle{
        color: var(--first-color);
        font-size: 1.5rem;
        cursor: pointer;
    }
    &__img{
        width: 35px;
        height: 35px;
        display: flex;
        justify-content: center;
        border-radius: 50%;
        overflow: hidden;

        & img{
            width: 40px;
        }
    }
}

/*===== NAV =====*/
.l-navbar{
    position: fixed;
    top: 0;
    left: -30%;
    width: var(--nav-width);
    height: 100vh;
    background-color: var(--first-color);
    padding: .5rem 1rem 0 0;
    transition: .5s;
    z-index: var(--z-fixed);
}

.nav{
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;

    &__logo, &__link{
        display: grid;
        grid-template-columns: max-content max-content;
        align-items: center;
        column-gap: 1rem;
        padding: .5rem 0 .5rem 1.5rem;
    }
    &__logo{
        margin-bottom: 2rem;

        &-icon{
            font-size: 1.25rem;
            color: var(--white-color);
        }
        &-name{
            color: var(--white-color);
            font-weight: 700;
        }
    }

    &__link{
        position: relative;
        color: var(--first-color-light);
        margin-bottom: 1.5rem;
        transition: .3s;

        &:hover{
            color: var(--white-color);
        }
    }

    &__icon{
        font-size: 1.25rem;
    }
}

/*Show navbar movil*/
.showa{
    left: 0;
}

/*Add padding body movil*/
.body-pd{
    padding-left: calc(var(--nav-width) + 1rem);
}

/*Active links*/
.active{
    color: var(--white-color);
    
    &::before{
        content: '';
        position: absolute;
        left: 0;
        width: 2px;
        height: 32px;
        background-color: var(--white-color);
    }
}

/* ===== MEDIA QUERIES=====*/
@media screen and (min-width: 768px){
    body{
        margin: calc(var(--header-height) + 1rem) 0 0 0;
        padding-left: calc(var(--nav-width) + 2rem);
    }
    .header{
        height: calc(var(--header-height) + 1rem);
        padding: 0 2rem 0 calc(var(--nav-width) + 2rem);

        &__img{
            width: 40px;
            height: 40px;
    
            & img{
                width: 45px;
            }
        }
    }

    .l-navbar{
        left: 0;
        padding: 1rem 1rem 0 0;
    }

    

    /*Add padding body desktop*/
    .body-pd{
        padding-left: calc(var(--nav-width) + 188px);
    }
}



