<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Test database connection
require 'partials/_dbconnect.php';

echo "<h1>PHP Test Page</h1>";

// Check if database connection is working
if ($conn) {
    echo "<p>Database connection successful!</p>";
    
    // Check if roomsdetails table exists and has data
    $sql = "SELECT * FROM roomsdetails";
    $result = mysqli_query($conn, $sql);
    
    if ($result) {
        $count = mysqli_num_rows($result);
        echo "<p>Found $count rooms in the database.</p>";
        
        echo "<h2>Rooms:</h2>";
        echo "<ul>";
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<li>Room: " . $row['room_no'] . ", Seater: " . $row['seater'] . ", Fees: " . $row['fees'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Error querying roomsdetails table: " . mysqli_error($conn) . "</p>";
    }
    
    // Check if userregistration table exists and has data
    $sql = "SELECT * FROM userregistration";
    $result = mysqli_query($conn, $sql);
    
    if ($result) {
        $count = mysqli_num_rows($result);
        echo "<p>Found $count users in the database.</p>";
        
        echo "<h2>Users:</h2>";
        echo "<ul>";
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<li>Registration No: " . $row['registration_no'] . ", Name: " . $row['first_name'] . " " . $row['last_name'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Error querying userregistration table: " . mysqli_error($conn) . "</p>";
    }
    
    // Check if courses table exists and has data
    $sql = "SELECT * FROM courses";
    $result = mysqli_query($conn, $sql);
    
    if ($result) {
        $count = mysqli_num_rows($result);
        echo "<p>Found $count courses in the database.</p>";
        
        echo "<h2>Courses:</h2>";
        echo "<ul>";
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<li>Course: " . $row['course_fn'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Error querying courses table: " . mysqli_error($conn) . "</p>";
    }
} else {
    echo "<p>Database connection failed: " . mysqli_connect_error() . "</p>";
}

// Check if session is working
session_start();
echo "<h2>Session Information:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check if URL parameters are working
echo "<h2>URL Parameters:</h2>";
echo "<pre>";
print_r($_GET);
echo "</pre>";

// Check PHP version
echo "<h2>PHP Information:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
?>

<h2>Test Links:</h2>
<ul>
    <li><a href="index.php">Home</a></li>
    <li><a href="index.php?page=hostelManage">Hostel Management</a></li>
    <li><a href="index.php?page=roomManage">Room Management</a></li>
    <li><a href="login.php">Login</a></li>
</ul>
