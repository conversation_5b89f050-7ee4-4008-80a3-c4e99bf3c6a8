<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
    <title>Login</title>
    <link rel = "icon" href ="/hostel-management-system/img/hostel-image.png" type = "image/x-icon">

<style>
    body{
		width: 100%;
	    height: calc(100%);
	    /*background: #007bff;*/
	}
	main#main{
		width:100%;
		height: calc(100%);
		background:white;
	}
	#login-right{
		position: absolute;
		right:0;
		width:40%;
		height: calc(100%);
		background:white;
		display: flex;
		align-items: center;
	}
	#login-left{
		position: absolute;
		left:0;
		width:60%;
		height: calc(100%);
		background:#00000061;
		display: flex;
		align-items: center;
	}
	#login-right .card{
		margin: auto
	}
	.logo {
	    margin: auto;
	    font-size: 8rem;
	    background: white;
	    border-radius: 50% 50%;
	    height: 29vh;
	    width: 13vw;
	    display: flex;
	    align-items: center;
	}
	.logo img{
		height: 80%;
		width: 80%;
		margin: auto
	}
</style>
</head>
<body>
    <main id="main" class=" bg-dark">
        <div id="login-left">
        <div class="logo">
            <img src="/hostel-management-system/img/hostel-bg.png" alt="">
        </div>
        </div>
        <div id="login-right">
        <div class="card col-md-8">
            <div class="card-body">
                <ul class="nav nav-tabs mb-3" id="loginTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="admin-tab" data-toggle="tab" href="#admin" role="tab" aria-controls="admin" aria-selected="true">Admin Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="student-tab" data-toggle="tab" href="#student" role="tab" aria-controls="student" aria-selected="false">Student Login</a>
                    </li>
                </ul>
                <div class="tab-content" id="loginTabsContent">
                    <!-- Admin Login Form -->
                    <div class="tab-pane fade show active" id="admin" role="tabpanel" aria-labelledby="admin-tab">
                        <form action="partials/_handleLogin.php" method="post">
                            <input type="hidden" name="user_type" value="admin">
                            <div class="form-group">
                                <label for="admin_username" class="control-label"><b>Username</b></label>
                                <input type="text" id="admin_username" name="username" class="form-control" placeholder="Enter Admin Username" required>
                            </div>
                            <div class="form-group">
                                <label for="admin_password" class="control-label"><b>Password</b></label>
                                <input type="password" id="admin_password" name="password" class="form-control" placeholder="Enter Password" required>
                            </div>
                            <center><button type="submit" class="btn-sm btn-block btn-wave col-md-4 btn-primary">Login</button></center>
                        </form>
                    </div>
                    <!-- Student Login Form -->
                    <div class="tab-pane fade" id="student" role="tabpanel" aria-labelledby="student-tab">
                        <form action="partials/_handleLogin.php" method="post">
                            <input type="hidden" name="user_type" value="student">
                            <div class="form-group">
                                <label for="student_regno" class="control-label"><b>Registration Number</b></label>
                                <input type="text" id="student_regno" name="regno" class="form-control" placeholder="Enter Registration Number" required>
                            </div>
                            <div class="form-group">
                                <label for="student_password" class="control-label"><b>Password</b></label>
                                <input type="password" id="student_password" name="password" class="form-control" placeholder="Enter Password" required>
                            </div>
                            <center><button type="submit" class="btn-sm btn-block btn-wave col-md-4 btn-primary">Login</button></center>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </main>
<?php
        if(isset($_GET['loginsuccess']) && $_GET['loginsuccess']=="false"){
        echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">
                <strong>Warning!</strong> Invalid Credentials
                <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span></button>
                </div>';
        }
?>

    <!-- Optional JavaScript -->
    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js" integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>
    <script src="https://unpkg.com/bootstrap-show-password@1.2.1/dist/bootstrap-show-password.min.js"></script>
</body>
</html>