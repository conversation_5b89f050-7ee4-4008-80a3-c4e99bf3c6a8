<?php
    include '_dbconnect.php';

if($_SERVER["REQUEST_METHOD"] == "POST") {

    if(isset($_POST['removeRoom'])) {
        // Sanitize input
        $Id = mysqli_real_escape_string($conn, $_POST["Id"]);

        // Create a lock name based on room ID
        $lockName = "room_management_" . $Id;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // Check if the room has any bookings
                $checkBookingsQuery = "SELECT COUNT(*) as booking_count FROM hostelbookings WHERE roomno = (SELECT room_no FROM roomsdetails WHERE id = '$Id')";
                $bookingsResult = executeQuery($checkBookingsQuery);
                $bookingsData = mysqli_fetch_assoc($bookingsResult);

                if ($bookingsData['booking_count'] > 0) {
                    // Room has bookings, cannot delete
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Cannot remove room. There are active bookings for this room.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // No bookings, proceed with deletion
                $sql = "DELETE FROM `roomsdetails` WHERE `id`='$Id'";
                $result = executeQuery($sql);

                if ($result) {
                    // Deletion successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Log the successful deletion
                    error_log("Room with ID $Id removed successfully");

                    echo "<script>alert('Room removed successfully');
                            window.location=document.referrer;
                          </script>";
                } else {
                    // Deletion failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Log the error
                    error_log("Room deletion failed for ID $Id: " . mysqli_error($conn));

                    echo "<script>alert('Failed to remove room: Database error');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Log the error
                error_log("Exception during room deletion: " . $e->getMessage());

                echo "<script>alert('An error occurred while removing the room. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }

    if(isset($_POST['createRoom'])) {
        // Sanitize input
        $roomno = mysqli_real_escape_string($conn, $_POST["roomno"]);
        $seater = mysqli_real_escape_string($conn, $_POST["seater"]);
        $fees = mysqli_real_escape_string($conn, $_POST["fees"]);

        // Create a lock name based on room number
        $lockName = "room_create_" . $roomno;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // Check if room number already exists
                $checkRoomQuery = "SELECT COUNT(*) as room_count FROM roomsdetails WHERE room_no = '$roomno'";
                $roomResult = executeQuery($checkRoomQuery);
                $roomData = mysqli_fetch_assoc($roomResult);

                if ($roomData['room_count'] > 0) {
                    // Room already exists, cannot create duplicate
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Room number already exists. Please use a different room number.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // Validate input data
                if (!is_numeric($seater) || $seater <= 0) {
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Invalid seater value. Please enter a positive number.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                if (!is_numeric($fees) || $fees <= 0) {
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Invalid fees value. Please enter a positive number.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // All checks passed, proceed with room creation
                $sql = "INSERT INTO `roomsdetails` (`seater`, `room_no`, `fees`) VALUES ('$seater', '$roomno', '$fees')";
                $result = executeQuery($sql);

                if ($result) {
                    // Creation successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Log the successful creation
                    error_log("New room $roomno added successfully");

                    echo "<script>alert('New Room Added Successfully.');
                            window.location=document.referrer;
                          </script>";
                } else {
                    // Creation failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Log the error
                    error_log("Room creation failed for room $roomno: " . mysqli_error($conn));

                    echo "<script>alert('Failed to add room: Database error');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Log the error
                error_log("Exception during room creation: " . $e->getMessage());

                echo "<script>alert('An error occurred while adding the room. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }

    if(isset($_POST['editRoom'])) {
        // Sanitize input
        $id = mysqli_real_escape_string($conn, $_POST["roomId"]);
        $seater = mysqli_real_escape_string($conn, $_POST['seater']);
        $room_no = mysqli_real_escape_string($conn, $_POST['roomno']);
        $fees = mysqli_real_escape_string($conn, $_POST['fees']);

        // Create a lock name based on room ID
        $lockName = "room_edit_" . $id;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // Get the current room details
                $getCurrentRoomQuery = "SELECT room_no, seater FROM roomsdetails WHERE id = '$id'";
                $currentRoomResult = executeQuery($getCurrentRoomQuery);
                $currentRoomData = mysqli_fetch_assoc($currentRoomResult);
                $currentRoomNo = $currentRoomData['room_no'];
                $currentSeater = $currentRoomData['seater'];

                // Check if the new room number already exists (if changing room number)
                if ($currentRoomNo != $room_no) {
                    $checkRoomQuery = "SELECT COUNT(*) as room_count FROM roomsdetails WHERE room_no = '$room_no' AND id != '$id'";
                    $roomResult = executeQuery($checkRoomQuery);
                    $roomData = mysqli_fetch_assoc($roomResult);

                    if ($roomData['room_count'] > 0) {
                        // Room number already exists, cannot update
                        rollbackTransaction();
                        releaseLock($lockName);
                        echo "<script>alert('Room number already exists. Please use a different room number.');
                                window.location=document.referrer;
                              </script>";
                        exit;
                    }
                }

                // If reducing seater capacity, check if it's possible
                if ($seater < $currentSeater) {
                    $checkBookingsQuery = "SELECT COUNT(*) as booking_count FROM hostelbookings WHERE roomno = '$currentRoomNo'";
                    $bookingsResult = executeQuery($checkBookingsQuery);
                    $bookingsData = mysqli_fetch_assoc($bookingsResult);
                    $currentBookings = $bookingsData['booking_count'];

                    if ($currentBookings > $seater) {
                        // Cannot reduce seater capacity below current bookings
                        rollbackTransaction();
                        releaseLock($lockName);
                        echo "<script>alert('Cannot reduce seater capacity. There are $currentBookings active bookings for this room.');
                                window.location=document.referrer;
                              </script>";
                        exit;
                    }
                }

                // Validate input data
                if (!is_numeric($seater) || $seater <= 0) {
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Invalid seater value. Please enter a positive number.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                if (!is_numeric($fees) || $fees <= 0) {
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Invalid fees value. Please enter a positive number.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // All checks passed, proceed with room update
                $sql = "UPDATE `roomsdetails` SET `seater`='$seater', `room_no`='$room_no', `fees`='$fees' WHERE `id`='$id'";
                $result = executeQuery($sql);

                if ($result) {
                    // If room number changed, update all bookings for this room
                    if ($currentRoomNo != $room_no) {
                        $updateBookingsQuery = "UPDATE hostelbookings SET roomno = '$room_no' WHERE roomno = '$currentRoomNo'";
                        executeQuery($updateBookingsQuery);
                    }

                    // Update successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Log the successful update
                    error_log("Room with ID $id updated successfully");

                    echo "<script>alert('Room updated successfully');
                            window.location=document.referrer;
                          </script>";
                } else {
                    // Update failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Log the error
                    error_log("Room update failed for ID $id: " . mysqli_error($conn));

                    echo "<script>alert('Failed to update room: Database error');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Log the error
                error_log("Exception during room update: " . $e->getMessage());

                echo "<script>alert('An error occurred while updating the room. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }
}
?>