<?php
$server = "localhost";
$username = "root";
$password = "root"; // MAMP default password is "root"
$database = "hostel_db";

// If using MAMP with default settings, you might need to specify the port
$conn = mysqli_connect($server, $username, $password, $database, 8889);
// If using standard port:
// $conn = mysqli_connect($server, $username, $password, $database);

if (!$conn){
    die("Error". mysqli_connect_error());
}

// Set the transaction isolation level to SERIALIZABLE for maximum consistency
// This ensures that transactions are completely isolated from each other
mysqli_query($conn, "SET SESSION TRANSACTION ISOLATION LEVEL SERIALIZABLE");

/**
 * Begin a database transaction
 * @return bool True if transaction started successfully
 */
function beginTransaction() {
    global $conn;
    return mysqli_begin_transaction($conn);
}

/**
 * Commit a database transaction
 * @return bool True if transaction committed successfully
 */
function commitTransaction() {
    global $conn;
    return mysqli_commit($conn);
}

/**
 * Rollback a database transaction
 * @return bool True if transaction rolled back successfully
 */
function rollbackTransaction() {
    global $conn;
    return mysqli_rollback($conn);
}

/**
 * Execute a query with error handling
 * @param string $sql SQL query to execute
 * @return mysqli_result|bool Result of the query or false on failure
 */
function executeQuery($sql) {
    global $conn;
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        error_log("Database error: " . mysqli_error($conn) . " in query: " . $sql);
    }
    return $result;
}

/**
 * Get the last inserted ID
 * @return int|string The ID generated for an AUTO_INCREMENT column
 */
function getLastInsertId() {
    global $conn;
    return mysqli_insert_id($conn);
}

/**
 * Acquire a named lock for concurrency control
 * @param string $lockName Name of the lock
 * @param int $timeout Timeout in seconds
 * @return bool True if lock acquired, false otherwise
 */
function acquireLock($lockName, $timeout = 10) {
    global $conn;
    $result = mysqli_query($conn, "SELECT GET_LOCK('$lockName', $timeout) as lockResult");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        return (bool)$row['lockResult'];
    }
    return false;
}

/**
 * Release a named lock
 * @param string $lockName Name of the lock to release
 * @return bool True if lock released, false otherwise
 */
function releaseLock($lockName) {
    global $conn;
    $result = mysqli_query($conn, "SELECT RELEASE_LOCK('$lockName') as releaseResult");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        return (bool)$row['releaseResult'];
    }
    return false;
}
?>
