<?php
session_start();

// Check if student is logged in
if(!isset($_SESSION['studentloggedin']) || $_SESSION['studentloggedin']!=true){
    header("location: /hostel-management-system/login.php");
    exit;
}

require '_dbconnect.php';
$studentId = $_SESSION['studentId'];
$studentRegNo = $_SESSION['studentregno'];

if($_SERVER["REQUEST_METHOD"] == "POST") {
    if(isset($_POST['createHostal'])) {
        // Sanitize all inputs
        $roomNo = mysqli_real_escape_string($conn, $_POST["roomNo"]);
        $seater = mysqli_real_escape_string($conn, $_POST["seater"]);
        $fees = mysqli_real_escape_string($conn, $_POST["fees"]);
        $total_ammount = mysqli_real_escape_string($conn, $_POST["total_ammount"]);
        $foodstatus = mysqli_real_escape_string($conn, $_POST["foodstatus"]);
        $startdate = mysqli_real_escape_string($conn, $_POST["startdate"]);
        $duration = mysqli_real_escape_string($conn, $_POST["duration"]);
        $course = mysqli_real_escape_string($conn, $_POST["course"]);
        $reg_no = mysqli_real_escape_string($conn, $_POST["reg_no"]);
        $first_name = mysqli_real_escape_string($conn, $_POST["first_name"]);
        $last_name = mysqli_real_escape_string($conn, $_POST["last_name"]);
        $gender = mysqli_real_escape_string($conn, $_POST["gender"]);
        $phone = mysqli_real_escape_string($conn, $_POST["phone"]);
        $emailid = mysqli_real_escape_string($conn, $_POST["emailid"]);
        $emg_no = mysqli_real_escape_string($conn, $_POST["emg_no"]);
        $guardian_name = mysqli_real_escape_string($conn, $_POST["guardian_name"]);
        $relation = mysqli_real_escape_string($conn, $_POST["relation"]);
        $contact_number = mysqli_real_escape_string($conn, $_POST["contact_number"]);
        $address = mysqli_real_escape_string($conn, $_POST["address"]);
        $city = mysqli_real_escape_string($conn, $_POST["city"]);
        $state = mysqli_real_escape_string($conn, $_POST["state"]);
        $postal_code = mysqli_real_escape_string($conn, $_POST["postal_code"]);

        // First, record this booking attempt in active_bookings
        $recordAttemptSql = "INSERT INTO active_bookings (room_no, student_id, status)
                            VALUES ('$roomNo', '$studentId', 'in_progress')";
        $recordResult = mysqli_query($conn, $recordAttemptSql);

        if (!$recordResult) {
            echo "<script>alert('Error recording booking attempt. Please try again.');
                    window.location='/hostel-management-system/student/book_room.php';
                  </script>";
            exit;
        }

        // Get the booking attempt ID
        $bookingAttemptId = mysqli_insert_id($conn);

        // Create a lock name based on room number to prevent concurrent bookings for the same room
        $lockName = "hostel_booking_room_" . $roomNo;

        // Try to acquire a lock for this room
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // First, check if there are still available seats in the room
                // Get the room details
                $roomDetailsQuery = "SELECT seater FROM roomsdetails WHERE room_no = '$roomNo'";
                $roomDetailsResult = executeQuery($roomDetailsQuery);

                // Count the current bookings for this room
                $bookingsCountQuery = "SELECT COUNT(*) as booked_seats FROM hostelbookings WHERE roomno = '$roomNo'";
                $bookingsCountResult = executeQuery($bookingsCountQuery);

                // Combine the results
                $availabilityResult = ($roomDetailsResult && $bookingsCountResult) ? true : false;

                if (!$availabilityResult) {
                    // Error in queries
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Update booking attempt status
                    mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

                    echo "<script>alert('Error checking room availability. Please try again.');
                            window.location='/hostel-management-system/student/book_room.php';
                          </script>";
                    exit;
                }

                if (!$roomDetailsResult || mysqli_num_rows($roomDetailsResult) == 0) {
                    // Room not found
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Update booking attempt status
                    mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

                    echo "<script>alert('Room not found. Please select a valid room.');
                            window.location='/hostel-management-system/student/book_room.php';
                          </script>";
                    exit;
                }

                // Get room details
                $roomDetailsData = mysqli_fetch_assoc($roomDetailsResult);
                $totalSeater = $roomDetailsData['seater'];

                // Get booking count
                $bookingsCountData = mysqli_fetch_assoc($bookingsCountResult);
                $bookedSeats = $bookingsCountData['booked_seats'];

                // Calculate available seats
                $availableSeats = $totalSeater - $bookedSeats;

                if ($availableSeats <= 0) {
                    // No seats available
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Update booking attempt status
                    mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

                    echo "<script>alert('Sorry, this room is already full. Please select another room.');
                            window.location='/hostel-management-system/student/book_room.php';
                          </script>";
                    exit;
                }

                // Check if student already has a booking
                $checkExistingBookingQuery = "SELECT id FROM hostelbookings WHERE regno = '$reg_no'";
                $existingBookingResult = executeQuery($checkExistingBookingQuery);

                if (mysqli_num_rows($existingBookingResult) > 0) {
                    // Student already has a booking
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Update booking attempt status
                    mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

                    echo "<script>alert('You already have a room booking. You cannot book multiple rooms.');
                            window.location='/hostel-management-system/student/index.php';
                          </script>";
                    exit;
                }

                // Double-check room capacity right before booking
                $finalCheckQuery = "SELECT
                                    (SELECT seater FROM roomsdetails WHERE room_no = '$roomNo') as total_capacity,
                                    (SELECT COUNT(*) FROM hostelbookings WHERE roomno = '$roomNo') as current_bookings";
                $finalCheckResult = executeQuery($finalCheckQuery);

                if (!$finalCheckResult) {
                    // Error in final check
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Update booking attempt status
                    mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

                    echo "<script>alert('Error during final capacity check. Please try again.');
                            window.location='/hostel-management-system/student/book_room.php';
                          </script>";
                    exit;
                }

                $finalCheckData = mysqli_fetch_assoc($finalCheckResult);
                $finalTotalCapacity = $finalCheckData['total_capacity'];
                $finalCurrentBookings = $finalCheckData['current_bookings'];

                if ($finalCurrentBookings >= $finalTotalCapacity) {
                    // Room is now full (race condition caught)
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Update booking attempt status
                    mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

                    echo "<script>alert('Sorry, this room has just been fully booked by another user. Please select another room.');
                            window.location='/hostel-management-system/student/book_room.php';
                          </script>";
                    exit;
                }

                // All checks passed, proceed with the booking
                $sql = "INSERT INTO `hostelbookings`(`roomno`, `seater`, `feespm`, `total_amount`, `foodstatus`, `stayfrom`, `duration`, `course`, `regno`, `firstName`, `lastName`, `gender`, `contactno`, `emailid`, `egycontactno`, `guardian_name`, `guardian_relation`, `guardian_contact`, `address`, `city`, `pin_code`, `state`)
                        VALUES ('$roomNo','$seater','$fees','$total_ammount','$foodstatus','$startdate','$duration','$course','$reg_no','$first_name','$last_name','$gender','$phone','$emailid','$emg_no','$guardian_name','$relation','$contact_number','$address','$city','$postal_code', '$state')";

                $result = executeQuery($sql);

                if ($result) {
                    // Booking successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Update booking attempt status
                    mysqli_query($conn, "UPDATE active_bookings SET status = 'completed' WHERE id = '$bookingAttemptId'");

                    // Log the successful booking
                    error_log("Hostel booking successful for student $reg_no in room $roomNo");

                    echo "<script>alert('Hostel Room Booked Successfully.');
                            window.location='/hostel-management-system/student/index.php';
                          </script>";
                } else {
                    // Booking failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Update booking attempt status
                    mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

                    // Log the error
                    error_log("Hostel booking failed for student $reg_no in room $roomNo: " . mysqli_error($conn));

                    echo "<script>alert('Booking Failed: Database error occurred.');
                            window.location='/hostel-management-system/student/book_room.php';
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Update booking attempt status
                mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

                // Log the error
                error_log("Exception during hostel booking: " . $e->getMessage());

                echo "<script>alert('An error occurred during booking: " . addslashes($e->getMessage()) . "');
                        window.location='/hostel-management-system/student/book_room.php';
                      </script>";
            }
        } else {
            // Could not acquire lock
            // Update booking attempt status
            mysqli_query($conn, "UPDATE active_bookings SET status = 'cancelled' WHERE id = '$bookingAttemptId'");

            echo "<script>alert('The room is currently being booked by another user. Please try again in a moment.');
                    window.location='/hostel-management-system/student/book_room.php';
                  </script>";
        }
    }
}
?>
