<?php
session_start();

// Check if it's a student logout
if(isset($_SESSION["studentloggedin"])) {
    // Clear student session variables
    unset($_SESSION["studentloggedin"]);
    unset($_SESSION["studentregno"]);
    unset($_SESSION["studentId"]);
    unset($_SESSION["studentname"]);

    // Redirect to login page
    header('Location: /hostel-management-system/login.php');
    exit;
}
// Check if it's an admin logout
else if(isset($_SESSION["adminloggedin"])) {
    // Clear admin session variables
    unset($_SESSION["adminloggedin"]);
    unset($_SESSION["adminusername"]);
    unset($_SESSION["adminuserId"]);

    // Redirect to login page
    header('Location: /hostel-management-system/login.php');
    exit;
}
// If no session variables are set, just redirect to login
else {
    header('Location: /hostel-management-system/login.php');
    exit;
}
?>
