<?php
    include '_dbconnect.php';

if($_SERVER["REQUEST_METHOD"] == "POST") {

    if(isset($_POST['removeUser'])) {
        // Sanitize input
        $Id = mysqli_real_escape_string($conn, $_POST["Id"]);

        // Create a lock name based on user ID
        $lockName = "user_remove_" . $Id;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // Check if the user has any hostel bookings
                $checkBookingsQuery = "SELECT COUNT(*) as booking_count FROM hostelbookings WHERE regno = (SELECT registration_no FROM userregistration WHERE id = '$Id')";
                $bookingsResult = executeQuery($checkBookingsQuery);
                $bookingsData = mysqli_fetch_assoc($bookingsResult);

                if ($bookingsData['booking_count'] > 0) {
                    // User has bookings, cannot delete
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Cannot remove user. This user has active hostel bookings.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // Get user details before deletion (for logging)
                $getUserQuery = "SELECT registration_no, first_name, last_name FROM userregistration WHERE id = '$Id'";
                $userResult = executeQuery($getUserQuery);

                if (mysqli_num_rows($userResult) == 0) {
                    // User not found
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('User not found.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                $userData = mysqli_fetch_assoc($userResult);
                $regNo = $userData['registration_no'];
                $fullName = $userData['first_name'] . ' ' . $userData['last_name'];

                // Delete the user
                $sql = "DELETE FROM `userregistration` WHERE `id`='$Id'";
                $result = executeQuery($sql);

                if ($result) {
                    // Deletion successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Log the successful deletion
                    error_log("User removed: $regNo ($fullName)");

                    echo "<script>alert('User removed successfully');
                            window.location=document.referrer;
                          </script>";
                } else {
                    // Deletion failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Log the error
                    error_log("User removal failed for ID $Id: " . mysqli_error($conn));

                    echo "<script>alert('Failed to remove user: Database error');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Log the error
                error_log("Exception during user removal: " . $e->getMessage());

                echo "<script>alert('An error occurred while removing the user. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }

    if(isset($_POST['createUser'])) {
        // Sanitize input
        $regno = mysqli_real_escape_string($conn, $_POST["registration"]);
        $firstName = mysqli_real_escape_string($conn, $_POST["firstName"]);
        $lastName = mysqli_real_escape_string($conn, $_POST["lastName"]);
        $email = mysqli_real_escape_string($conn, $_POST["email"]);
        $phone = mysqli_real_escape_string($conn, $_POST["phone"]);
        $gender = mysqli_real_escape_string($conn, $_POST["gender"]);

        // Create a lock name based on registration number
        $lockName = "user_create_" . $regno;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // Check if registration number already exists
                $checkRegNoQuery = "SELECT COUNT(*) as user_count FROM userregistration WHERE registration_no = '$regno'";
                $regNoResult = executeQuery($checkRegNoQuery);
                $regNoData = mysqli_fetch_assoc($regNoResult);

                if ($regNoData['user_count'] > 0) {
                    // Registration number already exists, cannot create duplicate
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Registration number already exists. Please use a different registration number.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // Check if email already exists
                $checkEmailQuery = "SELECT COUNT(*) as email_count FROM userregistration WHERE emailid = '$email'";
                $emailResult = executeQuery($checkEmailQuery);
                $emailData = mysqli_fetch_assoc($emailResult);

                if ($emailData['email_count'] > 0) {
                    // Email already exists, cannot create duplicate
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Email already exists. Please use a different email address.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // Validate input data
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Invalid email format. Please enter a valid email address.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                if (!preg_match('/^[0-9]{10}$/', $phone)) {
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Invalid phone number. Please enter a 10-digit phone number.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // All checks passed, proceed with user creation
                $sql = "INSERT INTO `userregistration` (`registration_no`, `first_name`, `last_name`, `emailid`, `contact_no`, `gender`) VALUES ('$regno', '$firstName', '$lastName', '$email', '$phone', '$gender')";
                $result = executeQuery($sql);

                if ($result) {
                    // Creation successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Log the successful creation
                    error_log("New user registered: $regno ($firstName $lastName)");

                    echo "<script>alert('Registration Successful.');
                            window.location=document.referrer;
                          </script>";
                } else {
                    // Creation failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Log the error
                    error_log("User registration failed for $regno: " . mysqli_error($conn));

                    echo "<script>alert('Failed to register user: Database error');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Log the error
                error_log("Exception during user registration: " . $e->getMessage());

                echo "<script>alert('An error occurred during registration. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }

    if(isset($_POST['editUser'])) {
        // Sanitize input
        $id = mysqli_real_escape_string($conn, $_POST["userId"]);
        $firstname = mysqli_real_escape_string($conn, $_POST['firstName']);
        $lastname = mysqli_real_escape_string($conn, $_POST['lastName']);
        $email = mysqli_real_escape_string($conn, $_POST['email']);
        $phone = mysqli_real_escape_string($conn, $_POST['phone']);
        $gender = mysqli_real_escape_string($conn, $_POST['gender']);

        // Create a lock name based on user ID
        $lockName = "user_edit_" . $id;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // Get the current user details
                $getCurrentUserQuery = "SELECT registration_no, emailid FROM userregistration WHERE id = '$id'";
                $currentUserResult = executeQuery($getCurrentUserQuery);

                if (mysqli_num_rows($currentUserResult) == 0) {
                    // User not found
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('User not found.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                $currentUserData = mysqli_fetch_assoc($currentUserResult);
                $currentEmail = $currentUserData['emailid'];
                $regNo = $currentUserData['registration_no'];

                // Check if the new email already exists (if changing email)
                if ($currentEmail != $email) {
                    $checkEmailQuery = "SELECT COUNT(*) as email_count FROM userregistration WHERE emailid = '$email' AND id != '$id'";
                    $emailResult = executeQuery($checkEmailQuery);
                    $emailData = mysqli_fetch_assoc($emailResult);

                    if ($emailData['email_count'] > 0) {
                        // Email already exists, cannot update
                        rollbackTransaction();
                        releaseLock($lockName);
                        echo "<script>alert('Email already exists. Please use a different email address.');
                                window.location=document.referrer;
                              </script>";
                        exit;
                    }
                }

                // Validate input data
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Invalid email format. Please enter a valid email address.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                if (!preg_match('/^[0-9]{10}$/', $phone)) {
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Invalid phone number. Please enter a 10-digit phone number.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // All checks passed, proceed with user update
                $sql = "UPDATE `userregistration` SET `first_name`='$firstname', `last_name`='$lastname', `emailid`='$email', `contact_no`='$phone', `gender`='$gender' WHERE `id`='$id'";
                $result = executeQuery($sql);

                if ($result) {
                    // Update successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Log the successful update
                    error_log("User updated: $regNo ($firstname $lastname)");

                    echo "<script>alert('User updated successfully');
                            window.location=document.referrer;
                          </script>";
                } else {
                    // Update failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Log the error
                    error_log("User update failed for ID $id: " . mysqli_error($conn));

                    echo "<script>alert('Failed to update user: Database error');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Log the error
                error_log("Exception during user update: " . $e->getMessage());

                echo "<script>alert('An error occurred while updating the user. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }

    if(isset($_POST['updateProfilePhoto'])) {
        // Sanitize input
        $id = mysqli_real_escape_string($conn, $_POST["userId"]);

        // Create a lock name based on user ID
        $lockName = "user_photo_update_" . $id;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Check if the user exists
                $checkUserQuery = "SELECT registration_no FROM userregistration WHERE id = '$id'";
                $userResult = executeQuery($checkUserQuery);

                if (mysqli_num_rows($userResult) == 0) {
                    // User not found
                    releaseLock($lockName);
                    echo "<script>alert('User not found.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                $userData = mysqli_fetch_assoc($userResult);
                $regNo = $userData['registration_no'];

                // Check if the uploaded file is an image
                $check = getimagesize($_FILES["userimage"]["tmp_name"]);
                if($check !== false) {
                    $newfilename = "person-".$id.".jpg";
                    $uploaddir = $_SERVER['DOCUMENT_ROOT'].'/hostel-management-system/img/';
                    $uploadfile = $uploaddir . $newfilename;

                    // Make sure the directory exists
                    if (!file_exists($uploaddir)) {
                        mkdir($uploaddir, 0777, true);
                    }

                    // Move the uploaded file
                    if (move_uploaded_file($_FILES['userimage']['tmp_name'], $uploadfile)) {
                        releaseLock($lockName);

                        // Log the successful upload
                        error_log("Profile photo updated for user $regNo");

                        echo "<script>alert('Profile photo updated successfully');
                                window.location=document.referrer;
                              </script>";
                    } else {
                        releaseLock($lockName);

                        // Log the error
                        error_log("Profile photo upload failed for user $regNo: " . error_get_last()['message']);

                        echo "<script>alert('Failed to upload profile photo: " . error_get_last()['message'] . "');
                                window.location=document.referrer;
                              </script>";
                    }
                } else {
                    releaseLock($lockName);

                    echo "<script>alert('Please select an image file to upload.');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                releaseLock($lockName);

                // Log the error
                error_log("Exception during profile photo update: " . $e->getMessage());

                echo "<script>alert('An error occurred while updating the profile photo. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }

    if(isset($_POST['removeProfilePhoto'])) {
        // Sanitize input
        $id = mysqli_real_escape_string($conn, $_POST["userId"]);

        // Create a lock name based on user ID
        $lockName = "user_photo_remove_" . $id;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Check if the user exists
                $checkUserQuery = "SELECT registration_no FROM userregistration WHERE id = '$id'";
                $userResult = executeQuery($checkUserQuery);

                if (mysqli_num_rows($userResult) == 0) {
                    // User not found
                    releaseLock($lockName);
                    echo "<script>alert('User not found.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                $userData = mysqli_fetch_assoc($userResult);
                $regNo = $userData['registration_no'];

                // Check if the photo exists
                $filename = $_SERVER['DOCUMENT_ROOT']."/hostel-management-system/img/person-".$id.".jpg";
                if (file_exists($filename)) {
                    // Delete the photo
                    if (unlink($filename)) {
                        releaseLock($lockName);

                        // Log the successful deletion
                        error_log("Profile photo removed for user $regNo");

                        echo "<script>alert('Profile photo removed successfully');
                                window.location=document.referrer;
                              </script>";
                    } else {
                        releaseLock($lockName);

                        // Log the error
                        error_log("Profile photo removal failed for user $regNo: " . error_get_last()['message']);

                        echo "<script>alert('Failed to remove profile photo: " . error_get_last()['message'] . "');
                                window.location=document.referrer;
                              </script>";
                    }
                } else {
                    releaseLock($lockName);

                    echo "<script>alert('No profile photo available.');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                releaseLock($lockName);

                // Log the error
                error_log("Exception during profile photo removal: " . $e->getMessage());

                echo "<script>alert('An error occurred while removing the profile photo. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }
}
?>