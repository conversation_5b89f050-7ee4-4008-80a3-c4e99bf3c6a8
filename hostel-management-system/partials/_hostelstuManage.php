<?php
    include '_dbconnect.php';

if($_SERVER["REQUEST_METHOD"] == "POST") {

    if(isset($_POST['removestudetails'])) {
        // Sanitize input
        $Id = mysqli_real_escape_string($conn, $_POST["Id"]);

        // Create a lock name based on booking ID
        $lockName = "hostel_booking_remove_" . $Id;

        // Try to acquire a lock
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // Get booking details before deletion (for logging)
                $getBookingQuery = "SELECT regno, roomno FROM hostelbookings WHERE id = '$Id'";
                $bookingResult = executeQuery($getBookingQuery);

                if (mysqli_num_rows($bookingResult) == 0) {
                    // Booking not found
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Booking not found.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                $bookingData = mysqli_fetch_assoc($bookingResult);
                $regNo = $bookingData['regno'];
                $roomNo = $bookingData['roomno'];

                // Delete the booking
                $sql = "DELETE FROM `hostelbookings` WHERE `id`='$Id'";
                $result = executeQuery($sql);

                if ($result) {
                    // Deletion successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Log the successful deletion
                    error_log("Hostel booking removed for student $regNo in room $roomNo");

                    echo "<script>alert('Student booking removed successfully');
                            window.location=document.referrer;
                          </script>";
                } else {
                    // Deletion failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Log the error
                    error_log("Hostel booking removal failed for ID $Id: " . mysqli_error($conn));

                    echo "<script>alert('Failed to remove booking: Database error');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Log the error
                error_log("Exception during hostel booking removal: " . $e->getMessage());

                echo "<script>alert('An error occurred while removing the booking. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock
            echo "<script>alert('The system is currently processing another request. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }
}
?>