<?php
if($_SERVER["REQUEST_METHOD"] == "POST"){
    include '_dbconnect.php';

    // Determine user type (admin or student)
    $userType = isset($_POST["user_type"]) ? $_POST["user_type"] : "admin";

    if($userType == "admin") {
        // Admin login
        $username = $_POST["username"];
        $password = md5($_POST["password"]);

        $sql = "SELECT * FROM users WHERE username='$username' AND password='$password'";
        $result = mysqli_query($conn, $sql);
        $num = mysqli_num_rows($result);

        if ($num == 1){
            $row = mysqli_fetch_assoc($result);
            $userId = $row['id'];
            session_start();
            $_SESSION['adminloggedin'] = true;
            $_SESSION['adminusername'] = $username;
            $_SESSION['adminuserId'] = $userId;
            header("location: /hostel-management-system/index.php?loginsuccess=true");
            exit();
        }
        else {
            header("location: /hostel-management-system/login.php?loginsuccess=false");
            exit();
        }
    }
    else if($userType == "student") {
        // Student login
        $regno = $_POST["regno"];
        $password = md5($_POST["password"]);

        // Check if student exists in userregistration and has a password
        $sql = "SELECT * FROM userregistration WHERE registration_no='$regno'";
        $result = mysqli_query($conn, $sql);

        if(mysqli_num_rows($result) == 1) {
            $row = mysqli_fetch_assoc($result);
            $userId = $row['id'];

            // Check if student has a password set in the student_passwords table
            $passwordSql = "SELECT * FROM student_passwords WHERE student_id='$userId' AND password='$password'";
            $passwordResult = mysqli_query($conn, $passwordSql);

            if(mysqli_num_rows($passwordResult) == 1) {
                // Password is correct
                session_start();
                $_SESSION['studentloggedin'] = true;
                $_SESSION['studentregno'] = $regno;
                $_SESSION['studentId'] = $userId;
                $_SESSION['studentname'] = $row['first_name'] . ' ' . $row['last_name'];
                header("location: /hostel-management-system/student/index.php?loginsuccess=true");
                exit();
            } else {
                // Password is incorrect
                header("location: /hostel-management-system/login.php?loginsuccess=false");
                exit();
            }
        } else {
            // Student not found
            header("location: /hostel-management-system/login.php?loginsuccess=false");
            exit();
        }
    } else {
        // Invalid user type
        header("location: /hostel-management-system/login.php?loginsuccess=false");
        exit();
    }
}
?>