<?php
    include '_dbconnect.php';

if($_SERVER["REQUEST_METHOD"] == "POST") {

    if(isset($_POST['createHostal'])) {
        // Sanitize input data to prevent SQL injection
        $roomNo = mysqli_real_escape_string($conn, $_POST["roomNo"]);
        $startdate = mysqli_real_escape_string($conn, $_POST["startdate"]);
        $seater = mysqli_real_escape_string($conn, $_POST["seater"]);
        $duration = mysqli_real_escape_string($conn, $_POST["duration"]);
        $foodstatus = mysqli_real_escape_string($conn, $_POST["foodstatus"]);
        $fees = mysqli_real_escape_string($conn, $_POST["fees"]);
        $total_ammount = mysqli_real_escape_string($conn, $_POST["total_ammount"]);
        $reg_no = mysqli_real_escape_string($conn, $_POST["reg_no"]);
        $first_name = mysqli_real_escape_string($conn, $_POST["first_name"]);
        $last_name = mysqli_real_escape_string($conn, $_POST["last_name"]);
        $emailid = mysqli_real_escape_string($conn, $_POST["emailid"]);
        $gender = mysqli_real_escape_string($conn, $_POST["gender"]);
        $phone = mysqli_real_escape_string($conn, $_POST["phone"]);
        $emg_no = mysqli_real_escape_string($conn, $_POST["emg_no"]);
        $course = mysqli_real_escape_string($conn, $_POST["course"]);
        $guardian_name = mysqli_real_escape_string($conn, $_POST["guardian_name"]);
        $relation = mysqli_real_escape_string($conn, $_POST["relation"]);
        $contact_number = mysqli_real_escape_string($conn, $_POST["contact_number"]);
        $state = mysqli_real_escape_string($conn, $_POST['state']);
        $address = mysqli_real_escape_string($conn, $_POST["address"]);
        $city = mysqli_real_escape_string($conn, $_POST["city"]);
        $postal_code = mysqli_real_escape_string($conn, $_POST["postal_code"]);

        // Create a lock name based on room number to prevent concurrent bookings for the same room
        $lockName = "hostel_booking_room_" . $roomNo;

        // Try to acquire a lock for this room
        if (acquireLock($lockName, 10)) {
            try {
                // Start a transaction
                beginTransaction();

                // First, check if there are still available seats in the room
                $checkAvailabilityQuery = "SELECT r.seater, COUNT(h.id) as booked_seats
                                          FROM roomsdetails r
                                          LEFT JOIN hostelbookings h ON r.room_no = h.roomno
                                          WHERE r.room_no = '$roomNo'
                                          GROUP BY r.seater";

                $availabilityResult = executeQuery($checkAvailabilityQuery);
                $availabilityData = mysqli_fetch_assoc($availabilityResult);

                $totalSeats = $availabilityData['seater'];
                $bookedSeats = $availabilityData['booked_seats'];
                $availableSeats = $totalSeats - $bookedSeats;

                // Check if there are available seats
                if ($availableSeats <= 0) {
                    // No seats available, rollback and show error
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('Sorry, this room is now fully booked. Please select another room.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // Check if the student is already booked in another room
                $checkStudentQuery = "SELECT id FROM hostelbookings WHERE regno = '$reg_no'";
                $studentResult = executeQuery($checkStudentQuery);

                if (mysqli_num_rows($studentResult) > 0) {
                    // Student already has a booking, rollback and show error
                    rollbackTransaction();
                    releaseLock($lockName);
                    echo "<script>alert('This student already has a hostel booking.');
                            window.location=document.referrer;
                          </script>";
                    exit;
                }

                // All checks passed, proceed with the booking
                $sql = "INSERT INTO `hostelbookings`(`roomno`, `seater`, `feespm`, `total_amount`, `foodstatus`, `stayfrom`, `duration`, `course`, `regno`, `firstName`, `lastName`, `gender`, `contactno`, `emailid`, `egycontactno`, `guardian_name`, `guardian_relation`, `guardian_contact`, `address`, `city`, `pin_code`, `state`)
                        VALUES ('$roomNo','$seater','$fees','$total_ammount','$foodstatus','$startdate','$duration','$course','$reg_no','$first_name','$last_name','$gender','$phone','$emailid','$emg_no','$guardian_name','$relation','$contact_number','$address','$city','$postal_code', '$state')";

                $result = executeQuery($sql);

                if ($result) {
                    // Booking successful, commit the transaction
                    commitTransaction();
                    releaseLock($lockName);

                    // Log the successful booking
                    error_log("Hostel booking successful for student $reg_no in room $roomNo");

                    echo "<script>alert('Hostel Booked Successfully.');
                            window.location=document.referrer;
                          </script>";
                } else {
                    // Booking failed, rollback the transaction
                    rollbackTransaction();
                    releaseLock($lockName);

                    // Log the error
                    error_log("Hostel booking failed for student $reg_no in room $roomNo: " . mysqli_error($conn));

                    echo "<script>alert('Booking Failed: Database error occurred.');
                            window.location=document.referrer;
                          </script>";
                }
            } catch (Exception $e) {
                // Handle any exceptions
                rollbackTransaction();
                releaseLock($lockName);

                // Log the error
                error_log("Exception during hostel booking: " . $e->getMessage());

                echo "<script>alert('An error occurred during booking. Please try again.');
                        window.location=document.referrer;
                      </script>";
            }
        } else {
            // Could not acquire lock, another booking might be in progress
            echo "<script>alert('The system is currently processing another booking. Please try again in a moment.');
                    window.location=document.referrer;
                  </script>";
        }
    }
}
?>