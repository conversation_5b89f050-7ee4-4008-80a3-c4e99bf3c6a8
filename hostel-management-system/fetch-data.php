<?php
require 'partials/_dbconnect.php';

// For seater retrieval
if(!empty($_POST['room'])){
    // Sanitize input
    $roomNo = mysqli_real_escape_string($conn, $_POST['room']);

    try {
        $roomsql = "SELECT seater FROM `roomsdetails` where room_no='$roomNo'";
        $roomResult = mysqli_query($conn, $roomsql);

        if (!$roomResult) {
            // Query failed
            error_log("Error fetching seater data for room $roomNo: " . mysqli_error($conn));
            echo "Error: Database error occurred";
            exit;
        }

        if (mysqli_num_rows($roomResult) == 0) {
            // Room not found
            echo "Room not found";
            exit;
        }

        $seaterdata = mysqli_fetch_assoc($roomResult);
        echo $seater = $seaterdata['seater'];
    } catch (Exception $e) {
        // Handle any exceptions
        error_log("Exception during seater data retrieval: " . $e->getMessage());
        echo "Error: An error occurred while retrieving seater data";
    }
}

// For fees retrieval
if(!empty($_POST['roomid'])){
    // Sanitize input
    $roomNo = mysqli_real_escape_string($conn, $_POST['roomid']);

    try {
        $roomsql = "SELECT fees FROM `roomsdetails` where room_no='$roomNo'";
        $roomResult = mysqli_query($conn, $roomsql);

        if (!$roomResult) {
            // Query failed
            error_log("Error fetching fees data for room $roomNo: " . mysqli_error($conn));
            echo "Error: Database error occurred";
            exit;
        }

        if (mysqli_num_rows($roomResult) == 0) {
            // Room not found
            echo "Room not found";
            exit;
        }

        $feesdata = mysqli_fetch_assoc($roomResult);
        echo $fees = $feesdata['fees'];
    } catch (Exception $e) {
        // Handle any exceptions
        error_log("Exception during fees data retrieval: " . $e->getMessage());
        echo "Error: An error occurred while retrieving fees data";
    }
}

if(!empty($_POST['roomsno'])){
    // Sanitize input
    $roomsNo = mysqli_real_escape_string($conn, $_POST['roomsno']);

    // Create a lock name based on room number for consistent reads
    $lockName = "room_availability_check_" . $roomsNo;

    // Try to acquire a lock
    if (acquireLock($lockName, 5)) { // Short timeout since this is just a read operation
        try {
            // First get the seater value from roomsdetails
            $roomDetailsSql = "SELECT seater FROM `roomsdetails` where room_no='$roomsNo'";
            $roomDetailsResult = mysqli_query($conn, $roomDetailsSql);

            if (!$roomDetailsResult || mysqli_num_rows($roomDetailsResult) == 0) {
                // Room not found
                releaseLock($lockName);
                echo json_encode(array("success"=>0, "msg"=>'<span style="color:red">Room not found.</span>'));
                exit;
            }

            $roomDetailsData = mysqli_fetch_assoc($roomDetailsResult);
            $totalSeater = $roomDetailsData['seater'];

            // Then count how many bookings exist for this room
            $roomssql = "SELECT count(*) as roomno FROM `hostelbookings` where roomno='$roomsNo'";
            $roomsResult = mysqli_query($conn, $roomssql);
            $availabledata = mysqli_fetch_assoc($roomsResult);
            $bookedSeats = $availabledata['roomno'];

            // Calculate available seats
            $availableSeats = $totalSeater - $bookedSeats;

            // Release the lock before responding
            releaseLock($lockName);

            if($bookedSeats > 0){
                if($availableSeats <= 0)
                {
                    echo json_encode(array("success"=>0, "msg"=>'<span style="color:red">Seats already full.</span>'));
                }
                else
                {
                    echo json_encode(array("success"=>1, "msg"=>'<span style="color:green">'.$availableSeats.' Seats are Available.</span>'));
                }
            }
            else
            {
                echo json_encode(array("success"=>1, "msg"=>'<span style="color:green">All '.$totalSeater.' Seats are Available.</span>'));
            }
        } catch (Exception $e) {
            // Handle any exceptions
            releaseLock($lockName);

            // Log the error
            error_log("Exception during room availability check: " . $e->getMessage());

            echo json_encode(array("success"=>0, "msg"=>'<span style="color:red">Error checking availability.</span>'));
        }
    } else {
        // Could not acquire lock, but this is just a read operation, so we can still proceed
        // First get the seater value from roomsdetails
        $roomDetailsSql = "SELECT seater FROM `roomsdetails` where room_no='$roomsNo'";
        $roomDetailsResult = mysqli_query($conn, $roomDetailsSql);

        if (!$roomDetailsResult || mysqli_num_rows($roomDetailsResult) == 0) {
            // Room not found
            echo json_encode(array("success"=>0, "msg"=>'<span style="color:red">Room not found.</span>'));
            exit;
        }

        $roomDetailsData = mysqli_fetch_assoc($roomDetailsResult);
        $totalSeater = $roomDetailsData['seater'];

        // Then count how many bookings exist for this room
        $roomssql = "SELECT count(*) as roomno FROM `hostelbookings` where roomno='$roomsNo'";
        $roomsResult = mysqli_query($conn, $roomssql);
        $availabledata = mysqli_fetch_assoc($roomsResult);
        $bookedSeats = $availabledata['roomno'];

        // Calculate available seats
        $availableSeats = $totalSeater - $bookedSeats;

        if($bookedSeats > 0){
            if($availableSeats <= 0)
            {
                echo json_encode(array("success"=>0, "msg"=>'<span style="color:red">Seats already full.</span>'));
            }
            else
            {
                echo json_encode(array("success"=>1, "msg"=>'<span style="color:green">'.$availableSeats.' Seats are Available.</span>'));
            }
        }
        else
        {
            echo json_encode(array("success"=>1, "msg"=>'<span style="color:green">All '.$totalSeater.' Seats are Available.</span>'));
        }
    }
}


if(!empty($_POST['regNo'])){
    // Sanitize input
    $regno = mysqli_real_escape_string($conn, $_POST['regNo']);

    // This is a read-only operation, so we don't need a transaction
    // But we'll add error handling and validation

    try {
        $usersql = "SELECT * FROM `userregistration` where registration_no='$regno'";
        $userResult = mysqli_query($conn, $usersql);

        if (!$userResult) {
            // Query failed
            error_log("Error fetching user data for registration number $regno: " . mysqli_error($conn));
            echo json_encode(array("error" => "Database error occurred"));
            exit;
        }

        if (mysqli_num_rows($userResult) == 0) {
            // User not found
            echo json_encode(array("error" => "User not found"));
            exit;
        }

        $userdata = mysqli_fetch_assoc($userResult);

        // Remove any sensitive data before sending
        unset($userdata['password']); // In case there's a password field

        $result = json_encode($userdata);
        echo $result;
    } catch (Exception $e) {
        // Handle any exceptions
        error_log("Exception during user data retrieval: " . $e->getMessage());
        echo json_encode(array("error" => "An error occurred while retrieving user data"));
    }
}
?>