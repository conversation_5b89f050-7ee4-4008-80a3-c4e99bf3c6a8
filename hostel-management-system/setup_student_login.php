<?php
require 'partials/_dbconnect.php';

// Read the SQL file
$sqlFile = file_get_contents('sql/create_student_passwords.sql');

// Split the SQL file into individual statements
$statements = explode(';', $sqlFile);

// Execute each statement
$success = true;
$errors = [];

foreach ($statements as $statement) {
    $statement = trim($statement);
    if (!empty($statement)) {
        // Replace DELIMITER statements for events/triggers
        if (strpos($statement, 'DELIMITER //') !== false) {
            $statement = str_replace('DELIMITER //', '', $statement);
            $statement = str_replace('END//', 'END', $statement);
            $statement = str_replace('DELIMITER ;', '', $statement);
        }
        
        if (!mysqli_query($conn, $statement)) {
            $success = false;
            $errors[] = "Error executing statement: " . mysqli_error($conn);
        }
    }
}

// Create a default student password for testing
// This will set the password to "password" for the first student in the userregistration table
$checkStudentSql = "SELECT * FROM userregistration LIMIT 1";
$studentResult = mysqli_query($conn, $checkStudentSql);

if (mysqli_num_rows($studentResult) > 0) {
    $studentRow = mysqli_fetch_assoc($studentResult);
    $studentId = $studentRow['id'];
    
    // Check if password already exists
    $checkPasswordSql = "SELECT * FROM student_passwords WHERE student_id = '$studentId'";
    $passwordResult = mysqli_query($conn, $checkPasswordSql);
    
    if (mysqli_num_rows($passwordResult) == 0) {
        // Insert default password (md5 hash of "password")
        $defaultPassword = md5("password");
        $insertPasswordSql = "INSERT INTO student_passwords (student_id, password) VALUES ('$studentId', '$defaultPassword')";
        
        if (!mysqli_query($conn, $insertPasswordSql)) {
            $success = false;
            $errors[] = "Error creating default student password: " . mysqli_error($conn);
        }
    }
}

// Output results
if ($success) {
    echo "Setup completed successfully!<br>";
    echo "Student login tables have been created.<br>";
    if (isset($studentId)) {
        echo "A default password has been set for student ID: " . $studentId . "<br>";
        echo "Registration Number: " . $studentRow['registration_no'] . "<br>";
        echo "Password: password<br>";
    } else {
        echo "No students found in the database. Please register students first.<br>";
    }
} else {
    echo "Setup failed with the following errors:<br>";
    foreach ($errors as $error) {
        echo "- " . $error . "<br>";
    }
}

echo "<br><a href='login.php'>Go to Login Page</a>";
?>
